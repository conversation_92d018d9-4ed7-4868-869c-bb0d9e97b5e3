const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

async function getCurrentUser(event) {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);
  
  // Get the authorization header
  const authHeader = event.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  
  const token = authHeader.substring(7);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    if (error) return null;
    return user;
  } catch (error) {
    return null;
  }
}

async function getUserSettings(userId, organizationId) {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);
  
  const { data, error } = await supabase
    .from('user_settings')
    .select('*')
    .eq('user_id', userId)
    .eq('organization_id', organizationId);

  if (error) throw error;
  return data;
}

async function updateMultipleSettings(userId, organizationId, updates) {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);
  
  const promises = updates.map(update => 
    supabase
      .from('user_settings')
      .upsert({
        user_id: userId,
        organization_id: organizationId,
        setting_key: update.key,
        setting_value: update.value,
        updated_at: new Date().toISOString()
      })
  );

  const results = await Promise.all(promises);
  
  // Check for errors
  for (const result of results) {
    if (result.error) throw result.error;
  }
  
  return await getUserSettings(userId, organizationId);
}

exports.handler = async (event, context) => {
  // Handle CORS
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      },
      body: '',
    };
  }

  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Content-Type': 'application/json',
  };

  try {
    const user = await getCurrentUser(event);
    
    if (!user) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Unauthorized' }),
      };
    }

    if (event.httpMethod === 'GET') {
      const url = new URL(event.rawUrl || `https://example.com${event.path}?${event.rawQuery || ''}`);
      const organizationId = url.searchParams.get('organizationId');

      if (!organizationId) {
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({ error: 'Organization ID is required' }),
        };
      }

      const settings = await getUserSettings(user.id, organizationId);
      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({ settings }),
      };

    } else if (event.httpMethod === 'POST') {
      const body = JSON.parse(event.body);
      const { organizationId, updates } = body;

      if (!organizationId || !Array.isArray(updates)) {
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({
            error: 'Organization ID and updates array are required'
          }),
        };
      }

      const updatedSettings = await updateMultipleSettings(
        user.id,
        organizationId,
        updates
      );

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({ 
          success: true, 
          settings: updatedSettings 
        }),
      };

    } else {
      return {
        statusCode: 405,
        headers,
        body: JSON.stringify({ error: 'Method not allowed' }),
      };
    }

  } catch (error) {
    console.error('Error in settings function:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' }),
    };
  }
}; 