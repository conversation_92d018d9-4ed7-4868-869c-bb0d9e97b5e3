const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

exports.handler = async (event, context) => {
  // Handle CORS
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
      },
      body: '',
    };
  }

  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Content-Type': 'application/json',
  };

  try {
    const supabase = createClient(supabaseUrl, supabaseAnonKey);
    
    // Get current user from auth
    const authHeader = event.headers.authorization || event.headers.Authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Unauthorized' }),
      };
    }
    
    const token = authHeader.substring(7);
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    if (authError || !user) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Invalid token' }),
      };
    }

    // Check what tables exist and what data we have
    const results = {};
    
    // Try to check users table
    try {
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();
      
      results.users_table = {
        exists: !usersError,
        error: usersError?.message,
        user_found: !!usersData,
        data: usersData
      };
    } catch (error) {
      results.users_table = {
        exists: false,
        error: error.message
      };
    }

    // Try to check organizations table
    try {
      const { data: orgsData, error: orgsError } = await supabase
        .from('organizations')
        .select('*')
        .limit(5);
      
      results.organizations_table = {
        exists: !orgsError,
        error: orgsError?.message,
        count: orgsData?.length || 0,
        sample_data: orgsData
      };
    } catch (error) {
      results.organizations_table = {
        exists: false,
        error: error.message
      };
    }

    // Return debug info
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        auth_user: {
          id: user.id,
          email: user.email,
          created_at: user.created_at
        },
        database_check: results,
        environment: {
          supabase_url: supabaseUrl ? 'Set' : 'Missing',
          supabase_key: supabaseAnonKey ? 'Set' : 'Missing'
        }
      }),
    };

  } catch (error) {
    console.error('Error in debug-db function:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error', details: error.message }),
    };
  }
};
