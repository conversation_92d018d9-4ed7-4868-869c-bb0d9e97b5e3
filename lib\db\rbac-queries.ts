import { createClient } from '@/lib/supabase/server';
import { createServiceClient } from '@/lib/supabase/server';
import { 
  Role, 
  UserRole, 
  RoleAuditEntry, 
  TeamInvitation, 
  TeamMember,
  Permission,
  CreateRoleData,
  UpdateRoleData,
  InviteTeamMemberData,
  EnhancedInviteTeamMemberData,
  CreateTeamMemberData,
  CreatedTeamMember
} from '@/types/roles';

export class RBACQueryError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'RBACQueryError';
  }
}

// Helper function to convert null to undefined
function nullToUndefined<T>(value: T | null): T | undefined {
  return value === null ? undefined : value;
}

// Helper function to parse permissions from database JSON
function parsePermissions(permissions: any): Permission[] {
  if (!permissions) return [];
  if (Array.isArray(permissions)) return permissions;
  if (typeof permissions === 'string') {
    try {
      return JSON.parse(permissions);
    } catch {
      return [];
    }
  }
  return [];
}

// Role management queries
export async function getRoles(organizationId: string, includeSystemOnly = false): Promise<Role[]> {
  const supabase = createClient();
  
  let query = supabase
    .from('roles')
    .select('*')
    .eq('organization_id', organizationId)
    .order('is_system_role', { ascending: false })
    .order('name', { ascending: true });

  if (includeSystemOnly) {
    query = query.eq('is_system_role', true);
  }

  const { data, error } = await query;

  if (error) {
    throw new RBACQueryError(`Failed to fetch roles: ${error.message}`, error.code);
  }

  return (data || []).map(role => ({
    id: role.id,
    organizationId: role.organization_id,
    name: role.name,
    description: nullToUndefined(role.description),
    permissions: parsePermissions(role.permissions),
    isSystemRole: role.is_system_role,
    createdBy: undefined,
    createdAt: role.created_at,
    updatedAt: role.updated_at,
  }));
}

export async function getRoleById(roleId: string, organizationId: string): Promise<Role | null> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('roles')
    .select('*')
    .eq('id', roleId)
    .eq('organization_id', organizationId)
    .single();

  if (error) {
    if (error.code === 'PGRST116') return null; // Not found
    throw new RBACQueryError(`Failed to fetch role: ${error.message}`, error.code);
  }

  return {
    id: data.id,
    organizationId: data.organization_id,
    name: data.name,
    description: nullToUndefined(data.description),
    permissions: parsePermissions(data.permissions),
    isSystemRole: data.is_system_role,
    createdBy: undefined,
    createdAt: data.created_at,
    updatedAt: data.updated_at,
  };
}

export async function createRole(
  organizationId: string, 
  roleData: CreateRoleData, 
  createdBy: string,
  isSystemRole = false
): Promise<Role> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('roles')
    .insert({
      organization_id: organizationId,
      name: roleData.name,
      description: roleData.description || null,
      permissions: roleData.permissions as any,
      is_system_role: isSystemRole,
    })
    .select()
    .single();

  if (error) {
    if (error.code === '23505') {
      throw new RBACQueryError('A role with this name already exists', 'DUPLICATE_NAME');
    }
    throw new RBACQueryError(`Failed to create role: ${error.message}`, error.code);
  }

  // Log the role creation
  await logRoleAction(createdBy, null, data.id, organizationId, 'role_created', null, roleData.permissions);

  return {
    id: data.id,
    organizationId: data.organization_id,
    name: data.name,
    description: nullToUndefined(data.description),
    permissions: parsePermissions(data.permissions),
    isSystemRole: data.is_system_role,
    createdBy: undefined,
    createdAt: data.created_at,
    updatedAt: data.updated_at,
  };
}

export async function updateRole(
  roleId: string,
  organizationId: string,
  updates: UpdateRoleData,
  updatedBy: string
): Promise<Role> {
  const supabase = createClient();
  
  // Get current role for audit log
  const currentRole = await getRoleById(roleId, organizationId);
  if (!currentRole) {
    throw new RBACQueryError('Role not found', 'NOT_FOUND');
  }

  // Check if it's a system role
  if (currentRole.isSystemRole) {
    throw new RBACQueryError('System roles cannot be modified', 'SYSTEM_ROLE');
  }

  const updateData: any = {};
  if (updates.name !== undefined) updateData.name = updates.name;
  if (updates.description !== undefined) updateData.description = updates.description || null;
  if (updates.permissions !== undefined) updateData.permissions = updates.permissions as any;

  const { data, error } = await supabase
    .from('roles')
    .update(updateData)
    .eq('id', roleId)
    .eq('organization_id', organizationId)
    .select()
    .single();

  if (error) {
    if (error.code === '23505') {
      throw new RBACQueryError('A role with this name already exists', 'DUPLICATE_NAME');
    }
    throw new RBACQueryError(`Failed to update role: ${error.message}`, error.code);
  }

  // Log the role modification
  await logRoleAction(
    updatedBy, 
    null, 
    roleId, 
    organizationId, 
    'role_updated', 
    currentRole.permissions, 
    parsePermissions(data.permissions)
  );

  return {
    id: data.id,
    organizationId: data.organization_id,
    name: data.name,
    description: nullToUndefined(data.description),
    permissions: parsePermissions(data.permissions),
    isSystemRole: data.is_system_role,
    createdBy: undefined,
    createdAt: data.created_at,
    updatedAt: data.updated_at,
  };
}

export async function deleteRole(
  roleId: string,
  organizationId: string,
  deletedBy: string
): Promise<void> {
  const supabase = createClient();
  
  // Get current role for validation and audit log
  const currentRole = await getRoleById(roleId, organizationId);
  if (!currentRole) {
    throw new RBACQueryError('Role not found', 'NOT_FOUND');
  }

  // Check if it's a system role
  if (currentRole.isSystemRole) {
    throw new RBACQueryError('System roles cannot be deleted', 'SYSTEM_ROLE');
  }

  // Check if role is assigned to any users
  const { data: assignments } = await supabase
    .from('user_roles')
    .select('id')
    .eq('role_id', roleId)
;

  if (assignments && assignments.length > 0) {
    throw new RBACQueryError('Cannot delete role that is assigned to users', 'ROLE_IN_USE');
  }

  const { error } = await supabase
    .from('roles')
    .delete()
    .eq('id', roleId)
    .eq('organization_id', organizationId);

  if (error) {
    throw new RBACQueryError(`Failed to delete role: ${error.message}`, error.code);
  }

  // Log the role deletion
  await logRoleAction(deletedBy, null, roleId, organizationId, 'role_deleted', currentRole.permissions, null);
}

// User role assignment queries
export async function getUserRoles(organizationId: string): Promise<UserRole[]> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('user_roles')
    .select(`
      *,
      roles (*)
    `)
    .eq('organization_id', organizationId)

    .order('assigned_at', { ascending: false });

  if (error) {
    throw new RBACQueryError(`Failed to fetch user roles: ${error.message}`, error.code);
  }

  return (data || []).map(userRole => ({
    id: userRole.id,
    userId: userRole.user_id,
    roleId: userRole.role_id,
    organizationId: userRole.organization_id,
    assignedBy: nullToUndefined(userRole.assigned_by),
    assignedAt: userRole.assigned_at,
    expiresAt: nullToUndefined(userRole.expires_at),
    isActive: true,
    role: userRole.roles ? {
      id: userRole.roles.id,
      organizationId: userRole.roles.organization_id,
      name: userRole.roles.name,
      description: nullToUndefined(userRole.roles.description),
      permissions: parsePermissions(userRole.roles.permissions),
      isSystemRole: userRole.roles.is_system_role,
      createdBy: undefined,
      createdAt: userRole.roles.created_at,
      updatedAt: userRole.roles.updated_at,
    } : undefined,
  }));
}

export async function assignRole(
  userId: string,
  roleId: string,
  organizationId: string,
  assignedBy: string,
  expiresAt?: string
): Promise<UserRole> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('user_roles')
    .insert({
      user_id: userId,
      role_id: roleId,
      organization_id: organizationId,
      assigned_by: assignedBy,
      expires_at: expiresAt,
    })
    .select(`
      *,
      roles (*)
    `)
    .single();

  if (error) {
    throw new RBACQueryError(`Failed to assign role: ${error.message}`, error.code);
  }

  // Log the role assignment
  await logRoleAction(assignedBy, userId, roleId, organizationId, 'assigned');

  return {
    id: data.id,
    userId: data.user_id,
    roleId: data.role_id,
    organizationId: data.organization_id,
    assignedBy: nullToUndefined(data.assigned_by),
    assignedAt: data.assigned_at,
    expiresAt: nullToUndefined(data.expires_at),
    isActive: true,
    role: data.roles ? {
      id: data.roles.id,
      organizationId: data.roles.organization_id,
      name: data.roles.name,
      description: nullToUndefined(data.roles.description),
      permissions: parsePermissions(data.roles.permissions),
      isSystemRole: data.roles.is_system_role,
      createdBy: undefined,
      createdAt: data.roles.created_at,
      updatedAt: data.roles.updated_at,
    } : undefined,
  };
}

// Team member queries
export async function getTeamMembers(organizationId: string): Promise<TeamMember[]> {
  // Use service client to bypass RLS restrictions while maintaining organization filtering
  const supabase = createServiceClient();

  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('organization_id', organizationId)
    .eq('is_active', true)
    .order('created_at', { ascending: true });

  if (error) {
    throw new RBACQueryError(`Failed to fetch team members: ${error.message}`, error.code);
  }
  return data?.map(user => ({
    id: user.id,
    email: user.email,
    fullName: nullToUndefined(user.full_name),
    avatarUrl: nullToUndefined(user.avatar_url),
    organizationId: nullToUndefined(user.organization_id),
    role: user.role,
    permissions: user.permissions,
    isActive: user.is_active,
    lastLoginAt: nullToUndefined(user.last_login_at),
    invitedBy: nullToUndefined(user.invited_by),
    invitedAt: nullToUndefined(user.invited_at),
    acceptedAt: nullToUndefined(user.accepted_at),
    createdAt: user.created_at,
    updatedAt: user.updated_at,
  })) || [];
}

// Team invitation queries
export async function getTeamInvitations(organizationId: string): Promise<TeamInvitation[]> {
  // Use service client to bypass RLS restrictions while maintaining organization filtering
  const supabase = createServiceClient();

  const { data, error } = await supabase
    .from('team_invitations')
    .select(`
      *,
      roles (*),
      inviter:users!team_invitations_invited_by_fkey (id, full_name, email)
    `)
    .eq('organization_id', organizationId)
    .order('created_at', { ascending: false });

  if (error) {
    throw new RBACQueryError(`Failed to fetch team invitations: ${error.message}`, error.code);
  }

  return (data || []).map(invitation => ({
    id: invitation.id,
    organizationId: invitation.organization_id,
    email: invitation.email,
    roleId: nullToUndefined(invitation.role_id),
    invitedBy: invitation.invited_by,
    expiresAt: invitation.expires_at,
    acceptedAt: nullToUndefined(invitation.accepted_at),
    status: invitation.status as 'pending' | 'accepted' | 'expired' | 'cancelled',
    token: invitation.token,
    createdAt: invitation.created_at,
    updatedAt: invitation.updated_at,
    role: invitation.roles ? {
      id: invitation.roles.id,
      organizationId: invitation.roles.organization_id,
      name: invitation.roles.name,
      description: nullToUndefined(invitation.roles.description),
      permissions: parsePermissions(invitation.roles.permissions),
      isSystemRole: invitation.roles.is_system_role,
      createdBy: undefined,
      createdAt: invitation.roles.created_at,
      updatedAt: invitation.roles.updated_at,
    } : undefined,
    inviter: undefined,
  }));
}

export async function createTeamInvitation(
  organizationId: string,
  invitationData: InviteTeamMemberData,
  invitedBy: string
): Promise<TeamInvitation> {
  const supabase = createClient();
  
  const token = crypto.randomUUID();
  
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(); // 7 days from now

  const { data, error } = await supabase
    .from('team_invitations')
    .insert({
      organization_id: organizationId,
      email: invitationData.email,
      role_id: invitationData.roleId || null,
      invited_by: invitedBy,
      expires_at: expiresAt,
      token,
    })
    .select(`
      *,
      roles (*),
      inviter:users!team_invitations_invited_by_fkey (id, full_name, email)
    `)
    .single();

  if (error) {
    throw new RBACQueryError(`Failed to create invitation: ${error.message}`, error.code);
  }

  return {
    id: data.id,
    organizationId: data.organization_id,
    email: data.email,
    roleId: nullToUndefined(data.role_id),
    invitedBy: data.invited_by,
    expiresAt: data.expires_at,
    acceptedAt: nullToUndefined(data.accepted_at),
    status: data.status as 'pending' | 'accepted' | 'expired' | 'cancelled',
    token: data.token,
    createdAt: data.created_at,
    updatedAt: data.updated_at,
    role: data.roles ? {
      id: data.roles.id,
      organizationId: data.roles.organization_id,
      name: data.roles.name,
      description: nullToUndefined(data.roles.description),
      permissions: parsePermissions(data.roles.permissions),
      isSystemRole: data.roles.is_system_role,
      createdBy: undefined,
      createdAt: data.roles.created_at,
      updatedAt: data.roles.updated_at,
    } : undefined,
    inviter: undefined,
  };
}

export async function createEnhancedTeamInvitation(
  organizationId: string,
  invitationData: EnhancedInviteTeamMemberData,
  invitedBy: string
): Promise<TeamInvitation> {
  const supabase = createClient();
  
  const token = crypto.randomUUID();
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(); // 7 days from now

  // First create the basic team invitation
  const { data: invitationRecord, error: invitationError } = await supabase
    .from('team_invitations')
    .insert({
      organization_id: organizationId,
      email: invitationData.email,
      role_id: invitationData.roleId || null,
      invited_by: invitedBy,
      expires_at: expiresAt,
      token,
    })
    .select(`
      *,
      roles (*),
      inviter:users!team_invitations_invited_by_fkey (id, full_name, email)
    `)
    .single();

  if (invitationError) {
    throw new RBACQueryError(`Failed to create invitation: ${invitationError.message}`, invitationError.code);
  }

  // Create a placeholder user record with the enhanced staff information
  // This will be used when the invitation is accepted
  const placeholderUserData = {
    id: crypto.randomUUID(), // Generate a temporary ID
    email: invitationData.email,
    full_name: invitationData.fullName || null,
    organization_id: organizationId,
    role: 'staff' as const, // Default role, will be updated when invitation is accepted
    is_active: false, // Inactive until invitation is accepted
    invited_by: invitedBy,
    invited_at: new Date().toISOString(),
    
    // Enhanced staff information
    phone_number: invitationData.phoneNumber || null,
    date_of_birth: invitationData.dateOfBirth || null,
    address: invitationData.address || null,
    city: invitationData.city || null,
    state: invitationData.state || null,
    postal_code: invitationData.postalCode || null,
    country: invitationData.country || null,
    emergency_contact: invitationData.emergencyContact || null,
    salary: invitationData.salary || null,
    employment_type: invitationData.employmentType || null,
    department: invitationData.department || null,
    position: invitationData.position || null,
    start_date: invitationData.startDate || null,
    end_date: invitationData.endDate || null,
    notes: invitationData.notes || null,
  };

  // Insert the placeholder user data
  const { error: userError } = await supabase
    .from('users')
    .insert(placeholderUserData);

  if (userError) {
    // If user creation fails, clean up the invitation
    await supabase.from('team_invitations').delete().eq('id', invitationRecord.id);
    throw new RBACQueryError(`Failed to create enhanced user profile: ${userError.message}`, userError.code);
  }

  return {
    id: invitationRecord.id,
    organizationId: invitationRecord.organization_id,
    email: invitationRecord.email,
    roleId: nullToUndefined(invitationRecord.role_id),
    invitedBy: invitationRecord.invited_by,
    expiresAt: invitationRecord.expires_at,
    acceptedAt: nullToUndefined(invitationRecord.accepted_at),
    status: invitationRecord.status as 'pending' | 'accepted' | 'expired' | 'cancelled',
    token: invitationRecord.token,
    createdAt: invitationRecord.created_at,
    updatedAt: invitationRecord.updated_at,
    role: invitationRecord.roles ? {
      id: invitationRecord.roles.id,
      organizationId: invitationRecord.roles.organization_id,
      name: invitationRecord.roles.name,
      description: nullToUndefined(invitationRecord.roles.description),
      permissions: parsePermissions(invitationRecord.roles.permissions),
      isSystemRole: invitationRecord.roles.is_system_role,
      createdBy: undefined,
      createdAt: invitationRecord.roles.created_at,
      updatedAt: invitationRecord.roles.updated_at,
    } : undefined,
    inviter: undefined,
  };
}

// Audit log queries
export async function getRoleAuditLog(
  organizationId: string,
  limit = 50,
  offset = 0,
  userId?: string,
  action?: string
): Promise<RoleAuditEntry[]> {
  const supabase = createClient();
  
  let query = supabase
    .from('role_audit_log')
    .select('*')
    .eq('organization_id', organizationId)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (userId) {
    query = query.eq('user_id', userId);
  }

  if (action) {
    query = query.eq('action', action);
  }

  const { data, error } = await query;

  if (error) {
    throw new RBACQueryError(`Failed to fetch audit log: ${error.message}`, error.code);
  }

  return (data || []).map(entry => ({
    id: entry.id,
    userId: entry.user_id,
    targetUserId: nullToUndefined(entry.target_user_id),
    roleId: nullToUndefined(entry.role_id),
    organizationId: entry.organization_id,
    action: entry.action as 'assigned' | 'removed' | 'modified' | 'role_created' | 'role_updated' | 'role_deleted',
    oldPermissions: parsePermissions(entry.old_permissions),
    newPermissions: parsePermissions(entry.new_permissions),
    metadata: typeof entry.metadata === 'object' && entry.metadata !== null && !Array.isArray(entry.metadata)
      ? entry.metadata as Record<string, any>
      : {},
    createdAt: entry.created_at,
  }));
}

// Permission checking queries
export async function getUserPermissions(userId: string, organizationId: string): Promise<Permission[]> {
  const supabase = createClient();
  
  // Get effective permissions by joining user roles and roles
  const { data, error } = await supabase
    .from('user_roles')
    .select(`
      roles!inner(permissions)
    `)
    .eq('user_id', userId)
    .eq('organization_id', organizationId)


  if (error) {
    throw new RBACQueryError(`Failed to get user permissions: ${error.message}`, error.code);
  }

  // Flatten the permissions array
  const permissions: Permission[] = [];
  if (Array.isArray(data)) {
    data.forEach(userRole => {
      const rolePermissions = parsePermissions(userRole.roles.permissions);
      permissions.push(...rolePermissions);
    });
  }

  // Remove duplicates
  const uniquePermissions = permissions.filter(
    (permission, index, self) =>
      index === self.findIndex(
        p => p.resource === permission.resource && 
             p.action === permission.action &&
             p.scope === permission.scope
      )
  );

  return uniquePermissions;
}

// Utility functions
async function logRoleAction(
  userId: string,
  targetUserId: string | null,
  roleId: string | null,
  organizationId: string,
  action: string,
  oldPermissions?: Permission[] | null,
  newPermissions?: Permission[] | null,
  metadata?: Record<string, any>
): Promise<void> {
  const supabase = createClient();
  
  await supabase
    .from('role_audit_log')
    .insert({
      user_id: userId,
      target_user_id: targetUserId,
      role_id: roleId,
      organization_id: organizationId,
      action,
      old_permissions: oldPermissions as any,
      new_permissions: newPermissions as any,
      metadata: metadata || {},
    });
}

// Initialize default roles for new organizations
export async function initializeSystemRoles(organizationId: string, createdBy: string): Promise<Role[]> {
  const { SYSTEM_ROLES } = await import('@/types/roles');
  
  const roles: Role[] = [];
  
  for (const [, roleData] of Object.entries(SYSTEM_ROLES)) {
    try {
      const role = await createRole(organizationId, {
        name: roleData.name,
        description: roleData.description,
        permissions: roleData.permissions as Permission[],
      }, createdBy, true); // Mark as system role
      roles.push(role);
    } catch (error) {
      console.error(`Failed to create system role ${roleData.name}:`, error);
    }
  }

  return roles;
}

export async function createTeamMember(
  organizationId: string,
  teamMemberData: CreateTeamMemberData,
  createdBy: string
): Promise<CreatedTeamMember> {
  const supabase = createServiceClient();
  
  // Generate a temporary password for the new user
  const tempPassword = crypto.randomUUID().slice(0, 12) + '!'; // 12 chars + special char
  
  try {
    // First, create the user in Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: teamMemberData.email,
      password: tempPassword,
      email_confirm: true, // Skip email confirmation
      user_metadata: {
        full_name: teamMemberData.fullName || null,
        needs_password_setup: true,
        created_by_organization: organizationId,
      },
    });

    if (authError) {
      throw new RBACQueryError(`Failed to create user account: ${authError.message}`, authError.code || 'AUTH_ERROR');
    }

    if (!authData.user) {
      throw new RBACQueryError('User creation succeeded but no user data returned', 'USER_DATA_MISSING');
    }

    // Create the user profile in our database
    const userProfileData = {
      id: authData.user.id,
      email: teamMemberData.email,
      full_name: teamMemberData.fullName || null,
      organization_id: organizationId,
      role: 'staff' as const, // Default role
      is_active: true,
      invited_by: createdBy,
      invited_at: new Date().toISOString(),
      accepted_at: new Date().toISOString(), // Immediately accepted since created directly
      
      // Enhanced staff information
      phone_number: teamMemberData.phoneNumber || null,
      date_of_birth: teamMemberData.dateOfBirth || null,
      address: teamMemberData.address || null,
      city: teamMemberData.city || null,
      state: teamMemberData.state || null,
      postal_code: teamMemberData.postalCode || null,
      country: teamMemberData.country || null,
      emergency_contact: teamMemberData.emergencyContact || null,
      salary: teamMemberData.salary || null,
      employment_type: teamMemberData.employmentType || null,
      department: teamMemberData.department || null,
      position: teamMemberData.position || null,
      start_date: teamMemberData.startDate || null,
      end_date: teamMemberData.endDate || null,
      notes: teamMemberData.notes || null,
    };

    const { error: profileError } = await supabase
      .from('users')
      .insert(userProfileData)
      .select()
      .single();

    if (profileError) {
      // If profile creation fails, clean up the auth user
      await supabase.auth.admin.deleteUser(authData.user.id);
      throw new RBACQueryError(`Failed to create user profile: ${profileError.message}`, profileError.code);
    }

    // Assign role if specified
    let assignedRoles: UserRole[] = [];
    if (teamMemberData.roleId) {
      try {
        const roleAssignment = await assignRole(
          authData.user.id,
          teamMemberData.roleId,
          organizationId,
          createdBy
        );
        assignedRoles = [roleAssignment];
      } catch (roleError) {
        console.warn('Failed to assign role to new user:', roleError);
        // Don't fail the entire operation if role assignment fails
      }
    }

    // Log the team member creation action
    try {
      await logRoleAction(
        createdBy,
        authData.user.id,
        teamMemberData.roleId || null,
        organizationId,
        'user_created',
        null,
        null,
        {
          creation_method: 'direct',
          temp_password_provided: true,
          role_assigned: !!teamMemberData.roleId,
        }
      );
    } catch (auditError) {
      console.warn('Failed to log team member creation:', auditError);
      // Don't fail the operation if audit logging fails
    }

    return {
      id: authData.user.id,
      email: teamMemberData.email,
      fullName: teamMemberData.fullName,
      organizationId,
      role: 'staff',
      isActive: true,
      needsPasswordSetup: true,
      assignedRoles,
      createdBy,
      createdAt: new Date().toISOString(),
      temporaryPassword: tempPassword, // Return this only once
    };

  } catch (error) {
    if (error instanceof RBACQueryError) {
      throw error;
    }
    throw new RBACQueryError(`Failed to create team member: ${error instanceof Error ? error.message : 'Unknown error'}`, 'CREATE_TEAM_MEMBER_ERROR');
  }
} 