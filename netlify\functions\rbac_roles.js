const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

async function getCurrentUser(event) {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);
  
  const authHeader = event.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  
  const token = authHeader.substring(7);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    if (error) return null;
    return user;
  } catch (error) {
    return null;
  }
}

async function hasPermission(userId, organizationId, permission) {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);
  
  try {
    // Check if user is owner (owners have all permissions)
    const { data: userCheck } = await supabase
      .from('users')
      .select('role, is_active')
      .eq('id', userId)
      .eq('organization_id', organizationId)
      .eq('is_active', true)
      .single();

    if (!userCheck) return false;
    if (userCheck.role === 'owner') return true;

    // Check user roles and permissions
    const { data, error } = await supabase
      .from('user_roles')
      .select(`
        roles!inner(permissions),
        users!inner(is_active)
      `)
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
      .eq('users.is_active', true);

    if (error || !data || data.length === 0) return false;

    // Check permissions from all user's roles
    for (const userRole of data) {
      const rolePermissions = userRole.roles.permissions;
      if (Array.isArray(rolePermissions)) {
        const hasWildcard = rolePermissions.some(p =>
          p.resource === '*' && p.action === '*'
        );
        if (hasWildcard) return true;

        const hasExactPermission = rolePermissions.some(p =>
          p.resource === permission.resource &&
          p.action === permission.action &&
          (!permission.scope || p.scope === permission.scope)
        );

        if (hasExactPermission) return true;
      }
    }

    return false;
  } catch (error) {
    console.error('Error checking permission:', error);
    return false;
  }
}

function parsePermissions(permissions) {
  if (!permissions) return [];
  if (Array.isArray(permissions)) return permissions;
  if (typeof permissions === 'string') {
    try {
      return JSON.parse(permissions);
    } catch {
      return [];
    }
  }
  return [];
}

async function getRoles(organizationId, includeSystemOnly = false) {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);
  
  let query = supabase
    .from('roles')
    .select('*')
    .eq('organization_id', organizationId)
    .order('is_system_role', { ascending: false })
    .order('name', { ascending: true });

  if (includeSystemOnly) {
    query = query.eq('is_system_role', true);
  }

  const { data, error } = await query;

  if (error) {
    throw new Error(`Failed to fetch roles: ${error.message}`);
  }

  return (data || []).map(role => ({
    id: role.id,
    organizationId: role.organization_id,
    name: role.name,
    description: role.description || undefined,
    permissions: parsePermissions(role.permissions),
    isSystemRole: role.is_system_role,
    createdBy: undefined,
    createdAt: role.created_at,
    updatedAt: role.updated_at,
  }));
}

// Permission constants
const PERMISSIONS = {
  ROLE_READ: { resource: 'role', action: 'read' },
  ROLE_CREATE: { resource: 'role', action: 'create' },
};

exports.handler = async (event, context) => {
  // Handle CORS
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      },
      body: '',
    };
  }

  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Content-Type': 'application/json',
  };

  try {
    const user = await getCurrentUser(event);
    if (!user) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Unauthorized' }),
      };
    }

    if (event.httpMethod === 'GET') {
      const url = new URL(event.rawUrl || `https://example.com${event.path}?${event.rawQuery || ''}`);
      const organizationId = url.searchParams.get('organizationId');
      
      if (!organizationId) {
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({ error: 'Organization ID is required' }),
        };
      }

      // Check permission to read roles
      const canRead = await hasPermission(user.id, organizationId, PERMISSIONS.ROLE_READ);
      if (!canRead) {
        return {
          statusCode: 403,
          headers,
          body: JSON.stringify({ error: 'Access denied. Required permission: role.read' }),
        };
      }

      const includeSystemOnly = url.searchParams.get('systemOnly') === 'true';
      const roles = await getRoles(organizationId, includeSystemOnly);

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({ roles }),
      };

    } else {
      return {
        statusCode: 405,
        headers,
        body: JSON.stringify({ error: 'Method not allowed' }),
      };
    }

  } catch (error) {
    console.error('Error in rbac/roles function:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' }),
    };
  }
}; 