const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

async function getCurrentUser(event) {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  // Get the authorization header (case-insensitive)
  const authHeader = event.headers.authorization || event.headers.Authorization;
  console.log('Auth header:', authHeader ? 'Present' : 'Missing');
  console.log('All headers:', Object.keys(event.headers));

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    console.log('No valid authorization header found');
    return null;
  }

  const token = authHeader.substring(7);
  console.log('Token length:', token.length);

  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    if (error) {
      console.log('Supabase auth error:', error);
      return null;
    }
    console.log('User authenticated:', user?.id);
    return user;
  } catch (error) {
    console.log('Exception in getCurrentUser:', error);
    return null;
  }
}

exports.handler = async (event, context) => {
  // Handle CORS
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
      },
      body: '',
    };
  }

  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Content-Type': 'application/json',
  };

  try {
    if (event.httpMethod !== 'GET') {
      return {
        statusCode: 405,
        headers,
        body: JSON.stringify({ error: 'Method not allowed' }),
      };
    }

    const user = await getCurrentUser(event);
    if (!user) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Unauthorized' }),
      };
    }

    // Get user organization info
    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    // First, try to get user data from the users table
    const { data: userData, error } = await supabase
      .from('users')
      .select('*, organization:organizations(*)')
      .eq('id', user.id)
      .single();

    if (error) {
      console.error('Error fetching user data from users table:', error);
      console.log('User ID:', user.id);
      console.log('Error details:', error);

      // If user doesn't exist in users table, return basic auth user info
      if (error.code === 'PGRST116') { // No rows returned
        console.log('User not found in users table, returning basic auth info');
        return {
          statusCode: 200,
          headers,
          body: JSON.stringify({
            user: {
              id: user.id,
              email: user.email,
              fullName: user.user_metadata?.full_name || null,
              role: 'user', // Default role
              organization_id: null,
              organization: null,
              isActive: true,
              lastLoginAt: user.last_sign_in_at,
              createdAt: user.created_at,
              needsOnboarding: true // Flag to indicate user needs to complete onboarding
            }
          }),
        };
      }

      // For other errors, return 500
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ error: 'Failed to fetch user data', details: error.message }),
      };
    }

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        user: {
          id: userData.id,
          email: userData.email,
          fullName: userData.full_name,
          role: userData.role,
          organization_id: userData.organization_id,
          organization: userData.organization,
          isActive: userData.is_active,
          lastLoginAt: userData.last_sign_in_at,
          createdAt: userData.created_at,
          needsOnboarding: false
        }
      }),
    };

  } catch (error) {
    console.error('Error in auth-user function:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' }),
    };
  }
}; 