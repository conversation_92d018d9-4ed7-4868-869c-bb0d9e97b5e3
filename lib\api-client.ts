import { createClient } from '@/lib/supabase/client';

interface APIResponse<T = any> {
  data?: T;
  error?: string;
  status: number;
}

/**
 * Makes an authenticated API request with proper headers
 */
export async function apiRequest<T = any>(
  url: string, 
  options: RequestInit = {}
): Promise<APIResponse<T>> {
  try {
    const supabase = createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.access_token) {
      return {
        error: 'No authentication token found',
        status: 401
      };
    }

    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session.access_token}`,
      ...options.headers,
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    const responseData = await response.json();

    return {
      data: response.ok ? responseData : undefined,
      error: response.ok ? undefined : responseData.error || 'Request failed',
      status: response.status
    };
  } catch (error) {
    console.error('API request error:', error);
    return {
      error: error instanceof Error ? error.message : 'Unknown error',
      status: 500
    };
  }
}

/**
 * GET request with authentication
 */
export async function apiGet<T = any>(url: string): Promise<APIResponse<T>> {
  return apiRequest<T>(url, { method: 'GET' });
}

/**
 * POST request with authentication
 */
export async function apiPost<T = any>(url: string, data?: any): Promise<APIResponse<T>> {
  return apiRequest<T>(url, {
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  });
}

/**
 * PUT request with authentication
 */
export async function apiPut<T = any>(url: string, data?: any): Promise<APIResponse<T>> {
  return apiRequest<T>(url, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
  });
}

/**
 * DELETE request with authentication
 */
export async function apiDelete<T = any>(url: string): Promise<APIResponse<T>> {
  return apiRequest<T>(url, { method: 'DELETE' });
} 