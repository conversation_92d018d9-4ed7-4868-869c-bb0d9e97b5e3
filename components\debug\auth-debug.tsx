'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';
import { apiGet } from '@/lib/api-client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function AuthDebug() {
  const [authState, setAuthState] = useState<any>(null);
  const [session, setSession] = useState<any>(null);
  const [apiTestResult, setApiTestResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const supabase = createClient();
    
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setAuthState({
        isAuthenticated: !!session,
        user: session?.user || null,
        accessToken: session?.access_token || null,
        tokenLength: session?.access_token?.length || 0
      });
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('Auth state changed:', event, session);
      setSession(session);
      setAuthState({
        isAuthenticated: !!session,
        user: session?.user || null,
        accessToken: session?.access_token || null,
        tokenLength: session?.access_token?.length || 0
      });
    });

    return () => subscription.unsubscribe();
  }, []);

  const testApiCall = async () => {
    setLoading(true);
    try {
      console.log('Testing API call...');
      const result = await apiGet('/api/auth/user');
      console.log('API result:', result);
      setApiTestResult(result);
    } catch (error) {
      console.error('API test error:', error);
      setApiTestResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const testDirectFetch = async () => {
    setLoading(true);
    try {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session?.access_token) {
        setApiTestResult({ error: 'No access token found' });
        return;
      }

      console.log('Testing direct fetch with token...');
      const response = await fetch('/api/auth/user', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      const data = await response.json();
      console.log('Direct fetch result:', { status: response.status, data });
      setApiTestResult({ 
        status: response.status, 
        data,
        headers: Object.fromEntries(response.headers.entries())
      });
    } catch (error) {
      console.error('Direct fetch error:', error);
      setApiTestResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4 p-4">
      <Card>
        <CardHeader>
          <CardTitle>Authentication Debug</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">Auth State:</h3>
            <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto">
              {JSON.stringify(authState, null, 2)}
            </pre>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Session Info:</h3>
            <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto">
              {JSON.stringify({
                hasSession: !!session,
                userId: session?.user?.id,
                email: session?.user?.email,
                tokenPresent: !!session?.access_token,
                tokenLength: session?.access_token?.length || 0,
                expiresAt: session?.expires_at
              }, null, 2)}
            </pre>
          </div>

          <div className="space-x-2">
            <Button onClick={testApiCall} disabled={loading}>
              Test API Client
            </Button>
            <Button onClick={testDirectFetch} disabled={loading}>
              Test Direct Fetch
            </Button>
          </div>

          {apiTestResult && (
            <div>
              <h3 className="font-semibold mb-2">API Test Result:</h3>
              <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto">
                {JSON.stringify(apiTestResult, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
