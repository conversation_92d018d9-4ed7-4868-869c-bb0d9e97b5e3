// Test script to manually test Netlify functions
const https = require('https');

async function testFunction(functionName, path = '', headers = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'bezorg.netlify.app',
      port: 443,
      path: `/.netlify/functions/${functionName}${path}`,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    console.log(`Testing: https://${options.hostname}${options.path}`);
    console.log('Headers:', options.headers);

    const req = https.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        console.log(`Status: ${res.statusCode}`);
        console.log(`Response: ${data}`);
        resolve({
          statusCode: res.statusCode,
          data: data,
          headers: res.headers
        });
      });
    });

    req.on('error', (error) => {
      console.error('Error:', error);
      reject(error);
    });

    req.end();
  });
}

async function testApiRoute(path, headers = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'bezorg.netlify.app',
      port: 443,
      path: path,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    console.log(`Testing API route: https://${options.hostname}${options.path}`);

    const req = https.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        console.log(`Status: ${res.statusCode}`);
        console.log(`Response: ${data}`);
        resolve({
          statusCode: res.statusCode,
          data: data,
          headers: res.headers
        });
      });
    });

    req.on('error', (error) => {
      console.error('Error:', error);
      reject(error);
    });

    req.end();
  });
}

async function runTests() {
  console.log('=== Testing Netlify Functions vs API Routes ===\n');

  // Test 1: Direct function call
  console.log('1. Testing direct function call:');
  try {
    await testFunction('auth_user');
  } catch (error) {
    console.error('Test 1 failed:', error.message);
  }
  console.log('\n');

  // Test 2: API route call
  console.log('2. Testing API route call:');
  try {
    await testApiRoute('/api/auth/user');
  } catch (error) {
    console.error('Test 2 failed:', error.message);
  }
  console.log('\n');

  // Test 3: Settings API route
  console.log('3. Testing settings API route:');
  try {
    await testApiRoute('/api/settings?organizationId=test-org');
  } catch (error) {
    console.error('Test 3 failed:', error.message);
  }
  console.log('\n');

  console.log('=== Tests completed ===');
}

runTests().catch(console.error);
