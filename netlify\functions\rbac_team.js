const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

async function getCurrentUser(event) {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);
  
  const authHeader = event.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  
  const token = authHeader.substring(7);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    if (error) return null;
    return user;
  } catch (error) {
    return null;
  }
}

async function hasPermission(userId, organizationId, permission) {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);
  
  try {
    // Check if user is owner (owners have all permissions)
    const { data: userCheck } = await supabase
      .from('users')
      .select('role, is_active')
      .eq('id', userId)
      .eq('organization_id', organizationId)
      .eq('is_active', true)
      .single();

    if (!userCheck) return false;
    if (userCheck.role === 'owner') return true;

    // Check user roles and permissions
    const { data, error } = await supabase
      .from('user_roles')
      .select(`
        roles!inner(permissions),
        users!inner(is_active)
      `)
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
      .eq('users.is_active', true);

    if (error || !data || data.length === 0) return false;

    // Check permissions from all user's roles
    for (const userRole of data) {
      const rolePermissions = userRole.roles.permissions;
      if (Array.isArray(rolePermissions)) {
        const hasWildcard = rolePermissions.some(p =>
          p.resource === '*' && p.action === '*'
        );
        if (hasWildcard) return true;

        const hasExactPermission = rolePermissions.some(p =>
          p.resource === permission.resource &&
          p.action === permission.action &&
          (!permission.scope || p.scope === permission.scope)
        );

        if (hasExactPermission) return true;
      }
    }

    return false;
  } catch (error) {
    console.error('Error checking permission:', error);
    return false;
  }
}

async function getTeamMembers(organizationId) {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);
  
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('organization_id', organizationId)
    .eq('is_active', true)
    .order('created_at', { ascending: false });

  if (error) throw error;

  return (data || []).map(user => ({
    id: user.id,
    email: user.email,
    fullName: user.full_name,
    role: user.role,
    isActive: user.is_active,
    lastLoginAt: user.last_sign_in_at,
    createdAt: user.created_at,
    updatedAt: user.updated_at,
  }));
}

async function getTeamInvitations(organizationId) {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);
  
  const { data, error } = await supabase
    .from('team_invitations')
    .select(`
      *,
      role:roles(*),
      inviter:users!team_invitations_invited_by_fkey(*)
    `)
    .eq('organization_id', organizationId)
    .order('created_at', { ascending: false });

  if (error) throw error;

  return (data || []).map(invitation => ({
    id: invitation.id,
    email: invitation.email,
    status: invitation.status,
    role: invitation.role ? {
      id: invitation.role.id,
      name: invitation.role.name,
      description: invitation.role.description,
    } : null,
    inviter: invitation.inviter ? {
      id: invitation.inviter.id,
      email: invitation.inviter.email,
      fullName: invitation.inviter.full_name,
    } : null,
    createdAt: invitation.created_at,
    expiresAt: invitation.expires_at,
  }));
}

// Permission constants
const PERMISSIONS = {
  USER_VIEW: { resource: 'user', action: 'read' },
  USER_INVITE: { resource: 'user', action: 'invite' },
};

exports.handler = async (event, context) => {
  // Handle CORS
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      },
      body: '',
    };
  }

  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Content-Type': 'application/json',
  };

  try {
    const user = await getCurrentUser(event);
    if (!user) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Unauthorized' }),
      };
    }

    if (event.httpMethod === 'GET') {
      const url = new URL(event.rawUrl || `https://example.com${event.path}?${event.rawQuery || ''}`);
      const organizationId = url.searchParams.get('organizationId');
      const type = url.searchParams.get('type');
      
      if (!organizationId) {
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({ error: 'Organization ID is required' }),
        };
      }

      // Check permission to read team data
      const canView = await hasPermission(user.id, organizationId, PERMISSIONS.USER_VIEW);
      if (!canView) {
        return {
          statusCode: 403,
          headers,
          body: JSON.stringify({ error: 'Access denied. Required permission: user.read' }),
        };
      }

      if (type === 'invitations') {
        const invitations = await getTeamInvitations(organizationId);
        return {
          statusCode: 200,
          headers,
          body: JSON.stringify({ invitations }),
        };
      } else {
        const members = await getTeamMembers(organizationId);
        return {
          statusCode: 200,
          headers,
          body: JSON.stringify({ members }),
        };
      }

    } else {
      return {
        statusCode: 405,
        headers,
        body: JSON.stringify({ error: 'Method not allowed' }),
      };
    }

  } catch (error) {
    console.error('Error in rbac/team function:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' }),
    };
  }
}; 